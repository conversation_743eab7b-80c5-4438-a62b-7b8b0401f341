/* Home 页面样式 */
.home-page {
  min-height: 100vh;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
/* 公司简介模块 */
.company-intro {
  padding: 60px 0;
  background: url('/uploads/sites/1012/2022/11/8c42f6797950f4aa3e1d8eb632b7ddb5.jpg') center/cover;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
}

/* 第一行：公司简介内容 */
.intro-content-row {
  display: flex;
  gap: 50px;
  align-items: flex-start;
  align-items: center;
  margin-bottom: 50px;
  position: relative;
  z-index: 2;
}

/* 左侧公司Logo (9/24 = 37.5%) */
.intro-left {
  flex: 0 0 37.5%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.company-logo img {
  max-width: 500px;
  height: auto;
}

/* 右侧公司简介文本 (15/24 = 62.5%) */
.intro-right {
  flex: 0 0 62.5%;
  padding-left: 50px;
}

.intro-text-content {
  /* padding: 30px; */
}

.intro-subtitle {
  color: #0064d8;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 15px 0;
  line-height: 1.5;
  text-align: justify;
}

.intro-title {
  color: #333;
  font-size: 34px;
  font-weight: bold;
  margin: 0 0 15px 0;
  line-height: 1.5;
  text-align: justify;
}

.intro-divider {
  margin: 15px 0;
  text-align: left;
}

.intro-divider img {
  display: block;
}

.intro-description {
  margin-bottom: 20px;
}

.intro-text {
  color: #333;
  font-size: 16px;
  line-height: 2;
  margin: 0;
  text-align: justify;
  text-justify: inter-ideograph;
}

.view-more-section {
  margin-top: 30px;
}

.view-more-btn {
  background: rgba(0, 129, 249, 1);
  color: #ffffff;
  border: 1px solid #ccc;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.view-more-btn:hover {
  background: rgba(0, 100, 216, 1);
  color: #ffffff;
}

/* 第二行：统计数据卡片 */
.statistics-row {
  position: relative;
  z-index: 2;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

/* 统计卡片样式 */
.stat-card {
  background: #ffffff;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: left;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right top;
  opacity: 0.3;
  z-index: 1;
}

.stat-card:nth-child(1)::before {
  background-image: url('/uploads/sites/1012/2022/11/79787277c39e51d6ed1a504b977c2032.png');
}

.stat-card:nth-child(2)::before {
  background-image: url('/uploads/sites/1012/2022/11/2f3e9140557119d54fbef7c2b625efa7.png');
}

.stat-card:nth-child(3)::before {
  background-image: url('/uploads/sites/1012/2022/11/1171bf6762c3ee5dd7c158f85ff103a9.png');
}

.stat-card .stat-number {
  display: flex;
  align-items: baseline;
  justify-content: flex-start;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
  transform: translateY(-20px);
}

.stat-card .counter {
  font-size: 60px;
  font-weight: bold;
  color: #0064d8;
  line-height: 1;
}

.stat-card .plus {
  font-size: 60px;
  font-weight: bold;
  color: #0064d8;
  margin-left: 5px;
}

.stat-card h3 {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 15px 0;
  position: relative;
  z-index: 2;
  transform: translateY(-55px);
}

.stat-card p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  position: relative;
  z-index: 2;
  text-align: justify;
  text-justify: inter-ideograph;
  transform: translateY(-40px);
}

/* 悬停动画效果 */
.hvr-float {
  transition: transform 0.3s ease;
}

.hvr-float:hover {
  transform: translateY(-10px);
}

.custom-yy {
  transition: all 0.3s ease;
}

.custom-yy:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}





/* 智慧生态模块 */
.smart-ecology {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  color: #fff;
  font-size: 32px;
  margin: 0 0 10px 0;
}

.section-header .section-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

.ecology-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.ecology-item {
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  transition: all 0.3s;
  cursor: pointer;
}

.ecology-item:hover {
  background: rgba(0, 100, 216, 1);
  transform: translateY(-5px);
}

.item-icon {
  margin-bottom: 20px;
}

.item-icon img {
  width: 60px;
  height: 60px;
}

.ecology-item h3 {
  color: #fff;
  font-size: 18px;
  margin: 0 0 10px 0;
}

.ecology-item p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

/* 人工智能模块 */
.ai-section {
  padding: 80px 0;
  background: rgba(0, 100, 216, 0.31);
}

.ai-section .section-header h2 {
  color: #fff;
}

.ai-category h3 {
  color: #fff;
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
}

.ai-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 600px;
  margin: 0 auto;
}

.ai-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  transition: all 0.3s;
  cursor: pointer;
}

.ai-item:hover {
  background: rgba(0, 100, 216, 1);
  transform: translateY(-5px);
}

.ai-item img {
  width: 50px;
  height: 50px;
  margin-bottom: 15px;
}

.ai-item span {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  display: block;
}

.ai-item p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin: 0;
  line-height: 1.3;
}

/* 数据统计模块 */
.statistics-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin: 0 auto;
}

.stat-card {
  background: #fff;
  padding: 50px;
  border-radius: 10px;
  text-align: left;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background: url('/uploads/sites/1012/2022/11/79787277c39e51d6ed1a504b977c2032.png') no-repeat;
  background-size: contain;
  opacity: 0.1;
}

.stat-number {
  display: flex;
  align-items: baseline;
  margin-bottom: 20px;
}

.counter {
  font-size: 60px;
  font-weight: bold;
  color: #0064d8;
  line-height: 1;
}

.plus {
  font-size: 24px;
  font-weight: bold;
  color: #0064d8;
  margin-left: 5px;
}

.stat-card h3 {
  color: #333;
  font-size: 18px;
  margin-bottom: 15px;
  font-weight: bold;
}

.stat-card p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* 新闻中心 */
.news-center {
  padding: 80px 0;
  background: #fff;
}

.section-subtitle {
  color: #333;
  font-size: 14px;
  text-align: center;
  margin: 0 0 10px 0;
}

.news-center .section-header h2 {
  color: #333;
  text-align: center;
}

.news-list {
  max-width: 800px;
  margin: 0 auto 40px;
}

.news-item {
  display: flex;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.news-item:hover {
  background: rgba(245, 246, 251, 1);
  transform: translateY(-2px);
}

.news-image {
  width: 300px;
  height: 225px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-content {
  flex: 1;
  padding: 30px;
  display: flex;
  align-items: center;
}

.news-content h3 {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.view-more-btn {
  display: block;
  margin: 0 auto;
  padding: 12px 24px;
  background: transparent;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.view-more-btn:hover {
  color: #0064d8;
  border-color: #0064d8;
}



/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.pulse-hover:hover {
  animation: pulse 0.3s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .company-intro {
    padding: 40px 0;
  }

  .intro-content-row {
    flex-direction: column;
    gap: 30px;
    margin-bottom: 30px;
  }

  .intro-left {
    flex: none;
    width: 100%;
  }

  .intro-right {
    flex: none;
    width: 100%;
    padding-left: 0;
  }

  .intro-text-content {
    padding: 20px;
  }

  .intro-title {
    font-size: 20px;
  }

  .intro-text {
    font-size: 14px;
    line-height: 1.8;
  }

  .statistics-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stat-card {
    padding: 30px 20px;
  }

  .stat-card .counter {
    font-size: 48px;
  }

  .stat-card .plus {
    font-size: 48px;
  }

  .stat-card h3 {
    font-size: 16px;
  }

  .stat-card p {
    font-size: 13px;
  }

  .ecology-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .ai-grid {
    grid-template-columns: 1fr;
  }

  .news-item {
    flex-direction: column;
  }

  .news-image {
    width: 100%;
    height: 200px;
  }
}
