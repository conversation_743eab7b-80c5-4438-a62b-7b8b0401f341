/* Home 页面样式 */
.home-page {
  min-height: 100vh;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
/* 公司简介模块 */
.company-intro {
  padding: 60px 0;
  background: url('/uploads/sites/1012/2022/11/8c42f6797950f4aa3e1d8eb632b7ddb5.jpg') center/cover;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
}

/* 第一行：公司简介内容 */
.intro-content-row {
  display: flex;
  gap: 50px;
  align-items: flex-start;
  align-items: center;
  margin-bottom: 50px;
  position: relative;
  z-index: 2;
}

/* 左侧公司Logo (9/24 = 37.5%) */
.intro-left {
  flex: 0 0 37.5%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.company-logo img {
  max-width: 500px;
  height: auto;
}

/* 右侧公司简介文本 (15/24 = 62.5%) */
.intro-right {
  flex: 0 0 62.5%;
  padding-left: 50px;
}

.intro-text-content {
  /* padding: 30px; */
}

.intro-subtitle {
  color: #0064d8;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 15px 0;
  line-height: 1.5;
  text-align: justify;
}

.intro-title {
  color: #333;
  font-size: 34px;
  font-weight: bold;
  margin: 0 0 15px 0;
  line-height: 1.5;
  text-align: justify;
}

.intro-divider {
  margin: 15px 0;
  text-align: left;
}

.intro-divider img {
  display: block;
}

.intro-description {
  margin-bottom: 20px;
}

.intro-text {
  color: #333;
  font-size: 16px;
  line-height: 2;
  margin: 0;
  text-align: justify;
  text-justify: inter-ideograph;
}

.view-more-section {
  margin-top: 30px;
}

.view-more-btn {
  background: rgba(0, 129, 249, 1);
  color: #ffffff;
  border: 1px solid #ccc;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.view-more-btn:hover {
  background: rgba(0, 100, 216, 1);
  color: #ffffff;
}

/* 第二行：统计数据卡片 */
.statistics-row {
  position: relative;
  z-index: 2;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

/* 统计卡片样式 */
.stat-card {
  background: #ffffff;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: left;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right top;
  opacity: 0.3;
  z-index: 1;
}

.stat-card:nth-child(1)::before {
  background-image: url('/uploads/sites/1012/2022/11/79787277c39e51d6ed1a504b977c2032.png');
}

.stat-card:nth-child(2)::before {
  background-image: url('/uploads/sites/1012/2022/11/2f3e9140557119d54fbef7c2b625efa7.png');
}

.stat-card:nth-child(3)::before {
  background-image: url('/uploads/sites/1012/2022/11/1171bf6762c3ee5dd7c158f85ff103a9.png');
}

.stat-card .stat-number {
  display: flex;
  align-items: baseline;
  justify-content: flex-start;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
  transform: translateY(-20px);
}

.stat-card .counter {
  font-size: 60px;
  font-weight: bold;
  color: #0064d8;
  line-height: 1;
}

.stat-card .plus {
  font-size: 60px;
  font-weight: bold;
  color: #0064d8;
  margin-left: 5px;
}

.stat-card h3 {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 15px 0;
  position: relative;
  z-index: 2;
  transform: translateY(-55px);
}

.stat-card p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  position: relative;
  z-index: 2;
  text-align: justify;
  text-justify: inter-ideograph;
  transform: translateY(-40px);
}

/* 悬停动画效果 */
.hvr-float {
  transition: transform 0.3s ease;
}

.hvr-float:hover {
  transform: translateY(-10px);
}

.custom-yy {
  transition: all 0.3s ease;
}

.custom-yy:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}





/* 智慧生态模块 */
.smart-ecology {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  color: #fff;
  font-size: 32px;
  margin: 0 0 10px 0;
}

.section-header .section-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

.ecology-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.ecology-item {
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  transition: all 0.3s;
  cursor: pointer;
}

.ecology-item:hover {
  background: rgba(0, 100, 216, 1);
  transform: translateY(-5px);
}

.item-icon {
  margin-bottom: 20px;
}

.item-icon img {
  width: 60px;
  height: 60px;
}

.ecology-item h3 {
  color: #fff;
  font-size: 18px;
  margin: 0 0 10px 0;
}

.ecology-item p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

/* 人工智能模块 */
.ai-section {
  padding: 80px 0;
  background: rgba(0, 100, 216, 0.31);
}

.ai-section .section-header h2 {
  color: #fff;
}

.ai-category h3 {
  color: #fff;
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
}

.ai-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 600px;
  margin: 0 auto;
}

.ai-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  transition: all 0.3s;
  cursor: pointer;
}

.ai-item:hover {
  background: rgba(0, 100, 216, 1);
  transform: translateY(-5px);
}

.ai-item img {
  width: 50px;
  height: 50px;
  margin-bottom: 15px;
}

.ai-item span {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  display: block;
}

.ai-item p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin: 0;
  line-height: 1.3;
}

/* 数据统计模块 */
.statistics-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin: 0 auto;
}

.stat-card {
  background: #fff;
  padding: 50px;
  border-radius: 10px;
  text-align: left;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background: url('/uploads/sites/1012/2022/11/79787277c39e51d6ed1a504b977c2032.png') no-repeat;
  background-size: contain;
  opacity: 0.1;
}

.stat-number {
  display: flex;
  align-items: baseline;
  margin-bottom: 20px;
}

.counter {
  font-size: 60px;
  font-weight: bold;
  color: #0064d8;
  line-height: 1;
}

.plus {
  font-size: 24px;
  font-weight: bold;
  color: #0064d8;
  margin-left: 5px;
}

.stat-card h3 {
  color: #333;
  font-size: 18px;
  margin-bottom: 15px;
  font-weight: bold;
}

.stat-card p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* 新闻中心 */
.news-center {
  padding: 80px 0;
  background: #fff;
}

.section-subtitle {
  color: #333;
  font-size: 14px;
  text-align: center;
  margin: 0 0 10px 0;
}

.news-center .section-header h2 {
  color: #333;
  text-align: center;
}

.news-list {
  max-width: 800px;
  margin: 0 auto 40px;
}

.news-item {
  display: flex;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.news-item:hover {
  background: rgba(245, 246, 251, 1);
  transform: translateY(-2px);
}

.news-image {
  width: 300px;
  height: 225px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-content {
  flex: 1;
  padding: 30px;
  display: flex;
  align-items: center;
}

.news-content h3 {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.view-more-btn {
  display: block;
  margin: 0 auto;
  padding: 12px 24px;
  background: transparent;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.view-more-btn:hover {
  color: #0064d8;
  border-color: #0064d8;
}



/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.pulse-hover:hover {
  animation: pulse 0.3s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .company-intro {
    padding: 40px 0;
  }

  .intro-content-row {
    flex-direction: column;
    gap: 30px;
    margin-bottom: 30px;
  }

  .intro-left {
    flex: none;
    width: 100%;
  }

  .intro-right {
    flex: none;
    width: 100%;
    padding-left: 0;
  }

  .intro-text-content {
    padding: 20px;
  }

  .intro-title {
    font-size: 20px;
  }

  .intro-text {
    font-size: 14px;
    line-height: 1.8;
  }

  .statistics-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stat-card {
    padding: 30px 20px;
  }

  .stat-card .counter {
    font-size: 48px;
  }

  .stat-card .plus {
    font-size: 48px;
  }

  .stat-card h3 {
    font-size: 16px;
  }

  .stat-card p {
    font-size: 13px;
  }

  .ecology-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .ai-grid {
    grid-template-columns: 1fr;
  }

  .news-item {
    flex-direction: column;
  }

  .news-image {
    width: 100%;
    height: 200px;
  }
}

/* 业务布局模块样式 */
.business-layout {
  padding: 60px 0;
  background: url('@/assets/images/bg.jpg') center/cover;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

.business-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.8) 0%, rgba(22, 33, 62, 0.8) 50%, rgba(15, 52, 96, 0.8) 100%);
  z-index: 1;
}

@media (max-width: 767px) {
  .business-layout {
    padding: 30px 0;
  }
}

/* 标题区域 */
.business-header {
  text-align: center;
  margin-bottom: 50px;
  position: relative;
  z-index: 2;
}

.business-subtitle {
  color: #ffffff;
  font-size: 16px;
  margin-bottom: 10px;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.business-title {
  color: #ffffff;
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 20px;
  line-height: 1.2;
}

.business-description {
  color: #ffffff;
  font-size: 16px;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
  text-align: justify;
}

@media (max-width: 767px) {
  .business-title {
    font-size: 32px;
  }

  .business-description {
    font-size: 14px;
    padding: 0 20px;
  }
}

/* 业务板块容器 */
.business-sections {
  display: flex;
  flex-direction: column;
  gap: 40px;
  position: relative;
  z-index: 2;
}

/* 单个业务板块 */
.business-section {
  width: 100%;
}

/* 板块标题 */
.section-header {
  text-align: center;
  margin-bottom: 30px;
}

.section-title {
  background-color: rgba(0, 100, 216, 0.31);
  color: #ffffff;
  font-size: 24px;
  font-weight: bold;
  padding: 15px 30px;
  margin: 0 auto 20px;
  display: inline-block;
  border-radius: 4px;
}

.section-divider {
  width: 100%;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 0 auto;
}

/* 轮播容器 */
.business-carousel {
  width: 100%;
}

.carousel-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 业务卡片 */
.business-card {
  background-color: rgba(0, 100, 216, 0.3);
  border-radius: 8px;
  padding: 30px;
  position: relative;
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/uploads/sites/1012/2022/11/892e009941da97e452b95f6ee3785e64.png');
  background-position: center bottom;
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0.1;
  z-index: 1;
}

.card-content {
  position: relative;
  z-index: 2;
}

.card-title {
  color: #ffffff;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px;
}

/* 卡片项目网格 */
.card-items {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  justify-items: center;
}

@media (max-width: 767px) {
  .card-items {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

/* 单个卡片项目 */
.card-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  background-image: url('/uploads/sites/1012/2022/11/2cc8948b47bc332aad648adc9c6f443a.png');
  background-position: right center;
  background-repeat: no-repeat;
  background-size: contain;
  min-height: 120px;
  width: 100%;
  max-width: 200px;
}

.card-item.hvr-sweep-to-top {
  position: relative;
  overflow: hidden;
}

.card-item.hvr-sweep-to-top::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 100, 216, 1);
  transition: top 0.3s ease;
  z-index: -1;
}

.card-item.hvr-sweep-to-top:hover::before {
  top: 0;
}

.card-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 100, 216, 0.3);
}

.card-item:hover * {
  color: #ffffff !important;
}

.item-icon {
  width: 36%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.item-icon img {
  max-width: 40px;
  height: auto;
}

.item-text {
  width: calc(100% - 36%);
  color: #ffffff;
  font-size: 14px;
  text-align: center;
  line-height: 1;
  font-weight: 500;
}

/* 装饰元素 */
.business-decoration {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 1;
  pointer-events: none;
}

.decoration-left {
  position: absolute;
  left: 7%;
  top: 50%;
  transform: translateY(-50%) translateX(12px);
  max-width: 30px;
  height: auto;
  opacity: 0.6;
}

.decoration-right {
  position: absolute;
  right: 7%;
  top: 50%;
  transform: translateY(-50%) translateX(-12px) scaleX(-1);
  max-width: 30px;
  height: auto;
  opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .business-card {
    padding: 20px;
    margin: 0 20px;
  }

  .card-title {
    font-size: 22px;
    margin-bottom: 20px;
  }

  .card-item {
    min-height: 100px;
    padding: 15px;
  }

  .item-icon img {
    max-width: 30px;
  }

  .item-text {
    font-size: 12px;
  }

  .decoration-left,
  .decoration-right {
    display: none;
  }
}

@media (min-width: 768px) {
  .business-sections {
    gap: 50px;
  }

  .section-title {
    font-size: 28px;
    padding: 20px 40px;
  }

  .business-card {
    padding: 40px;
  }

  .card-title {
    font-size: 32px;
    margin-bottom: 40px;
  }
}

@media (min-width: 992px) {
  .business-card {
    padding: 50px;
  }

  .card-items {
    gap: 30px;
  }

  .card-item {
    max-width: 250px;
    min-height: 140px;
    padding: 25px;
  }

  .item-icon img {
    max-width: 50px;
  }

  .item-text {
    font-size: 16px;
  }
}

@media (min-width: 1200px) {
  .business-card {
    padding: 60px;
  }

  .decoration-left {
    transform: translateY(-50%) translateX(16px);
  }

  .decoration-right {
    transform: translateY(-50%) translateX(-16px) scaleX(-1);
  }
}

@media (min-width: 1360px) {
  .decoration-left {
    transform: translateY(-50%) translateX(20px);
  }

  .decoration-right {
    transform: translateY(-50%) translateX(-20px) scaleX(-1);
  }
}

@media (min-width: 1600px) {
  .decoration-left {
    transform: translateY(-50%) translateX(23px);
  }

  .decoration-right {
    transform: translateY(-50%) translateX(-23px) scaleX(-1);
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 业务卡片进入动画 */
.business-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.business-card.animate-in {
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.8s ease forwards;
}

/* 卡片项目悬停效果增强 */
.card-item {
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 100, 216, 0.4);
}

/* 标题动画 */
.business-header {
  animation: fadeInUp 1s ease 0.2s both;
}

.section-title {
  transition: all 0.3s ease;
}

.section-title:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 100, 216, 0.3);
}
