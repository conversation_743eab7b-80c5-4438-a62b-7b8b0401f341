<template>
  <div class="home-page">
    <!-- 主要内容 -->
    <main class="page-content">
      <!-- 轮播图 -->
      <HeroSlider
        :slides="slides"
        @learn-more="handleLearnMore"
        @slide-change="handleSlideChange"
      />

      <!-- 公司简介模块 -->
      <section class="company-intro" id="集团简介">
        <div class="container">
          <!-- 第一行：公司简介内容 -->
          <div class="intro-content-row">
            <!-- 左侧公司Logo -->
            <div class="intro-left">
              <div class="company-logo">
                <img src="/uploads/sites/1012/2022/11/18b3630d73e2d35aa818fdedb18cd8e0.png" alt="公司标识" />
              </div>
            </div>

            <!-- 右侧公司简介文本 -->
            <div class="intro-right">
              <div class="intro-text-content">
                <p class="intro-subtitle">WHO WE ARE</p>
                <h2 class="intro-title">數字起源 (香港) 有限公司</h2>
                <div class="intro-divider">
                  <img src="/uploads/sites/1012/2022/11/15d5fcbdf0b8f197d7a8bb0f076ad841.jpg" alt="分割线" width="101" height="6" />
                </div>
                <div class="intro-description">
                  <p class="intro-text">
                    系環球數科股份有限公司（以下簡稱"集團"）下屬機構，作為集團海外總部、創新研發中心、全球運營中心，
                    公司將基於集團 20 餘年的行業沉澱及技術積累，依托香港在區位、政策、人才等方面的優勢，
                    聚焦前沿技術研究、技術驗證、產品預研及海外市場拓展，形成國內與海外市場雙循環的出海新格局。
                    數字起源，連接無限可能，賦能美好生活。
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 第二行：统计数据卡片 -->
          <div class="statistics-row">
            <div class="statistics-grid">
              <div class="stat-card hvr-float custom-yy">
                <div class="stat-number">
                  <span class="counter" data-target="24">24</span>
                  <span class="plus">+</span>
                </div>
                <div class="stat-label">年行業沉澱</div>
              </div>
              <div class="stat-card hvr-float custom-yy">
                <div class="stat-number">
                  <span class="counter" data-target="1000">1000</span>
                  <span class="plus">+</span>
                </div>
                <div class="stat-label">項目案例</div>
              </div>
              <div class="stat-card hvr-float custom-yy">
                <div class="stat-number">
                  <span class="counter" data-target="100">100</span>
                  <span class="plus">+</span>
                </div>
                <div class="stat-label">合作夥伴</div>
              </div>
              <div class="stat-card hvr-float custom-yy">
                <div class="stat-number">
                  <span class="counter" data-target="50">50</span>
                  <span class="plus">+</span>
                </div>
                <div class="stat-label">專利技術</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 业务布局模块 -->
      <section class="business-layout" id="業務佈局">
        <div class="container">
          <!-- 标题区域 -->
          <div class="business-header">
            <p class="business-subtitle">WHAT WE DO</p>
            <h2 class="business-title">業務佈局</h2>
            <p class="business-description">
              以"雲鏈一體" 鍛造可信數字底座，夯築數字安全屏障可信可控；以"雲智共生" 賦能行業應用創新，推動數據要素價值加速釋放。
            </p>
          </div>

          <!-- 业务板块展示 -->
          <div class="business-sections">
            <!-- 行业云板块 -->
            <div class="business-section industry-cloud">
              <div class="section-header">
                <h3 class="section-title">行業雲</h3>
                <div class="section-divider"></div>
              </div>

              <div class="business-carousel">
                <div class="carousel-container">
                  <!-- 智慧文旅 -->
                  <div class="business-card">
                    <div class="card-background"></div>
                    <div class="card-content">
                      <h4 class="card-title">智慧文旅</h4>
                      <div class="card-items">
                        <div class="card-item hvr-sweep-to-top">
                          <div class="item-icon">
                            <img src="/uploads/sites/1012/2022/11/22093492e9cda93d64e9df82cca6706e.png" alt="數字景區" />
                          </div>
                          <div class="item-text">數字景區</div>
                        </div>
                        <div class="card-item hvr-sweep-to-top">
                          <div class="item-icon">
                            <img src="/uploads/sites/1012/2022/11/cca01f8608a48e4299e118c4f86a71fb.png" alt="智能營銷" />
                          </div>
                          <div class="item-text">智能營銷</div>
                        </div>
                        <div class="card-item hvr-sweep-to-top">
                          <div class="item-icon">
                            <img src="/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png" alt="智慧生態" />
                          </div>
                          <div class="item-text">智慧生態</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 智慧生态板块 -->
            <div class="business-section smart-ecology-section">
              <div class="section-header">
                <h3 class="section-title">智慧生態</h3>
                <div class="section-divider"></div>
              </div>

              <div class="business-carousel">
                <div class="carousel-container">
                  <div class="business-card">
                    <div class="card-background"></div>
                    <div class="card-content">
                      <h4 class="card-title">智慧生態</h4>
                      <div class="card-items">
                        <div class="card-item hvr-sweep-to-top">
                          <div class="item-icon">
                            <img src="/uploads/sites/1012/2022/11/073346750cbf5e05e3ea59df9549578a.png" alt="生態監測" />
                          </div>
                          <div class="item-text">生態監測</div>
                        </div>
                        <div class="card-item hvr-sweep-to-top">
                          <div class="item-icon">
                            <img src="/uploads/sites/1012/2022/11/d1fba75503642fc884e617bc2a146ff9.png" alt="災害預警" />
                          </div>
                          <div class="item-text">災害預警</div>
                        </div>
                        <div class="card-item hvr-sweep-to-top">
                          <div class="item-icon">
                            <img src="/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png" alt="森林防火" />
                          </div>
                          <div class="item-text">森林防火</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 人工智能板块 -->
            <div class="business-section artificial-intelligence">
              <div class="section-header">
                <h3 class="section-title">人工智能</h3>
                <div class="section-divider"></div>
              </div>

              <div class="business-carousel">
                <div class="carousel-container">
                  <div class="business-card">
                    <div class="card-background"></div>
                    <div class="card-content">
                      <h4 class="card-title">垂類大模型</h4>
                      <div class="card-items">
                        <div class="card-item hvr-sweep-to-top">
                          <div class="item-icon">
                            <img src="/uploads/sites/1012/2024/07/83841dc18fcc7f97315235a2ac571d46.png" alt="智能分析" />
                          </div>
                          <div class="item-text">智能分析</div>
                        </div>
                        <div class="card-item hvr-sweep-to-top">
                          <div class="item-icon">
                            <img src="/uploads/sites/1012/2022/11/cca01f8608a48e4299e118c4f86a71fb.png" alt="智能決策" />
                          </div>
                          <div class="item-text">智能決策</div>
                        </div>
                        <div class="card-item hvr-sweep-to-top">
                          <div class="item-icon">
                            <img src="/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png" alt="智能應用" />
                          </div>
                          <div class="item-text">智能應用</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 装饰元素 -->
          <div class="business-decoration">
            <img src="/uploads/sites/1012/2022/11/93bf81726314b734d59603e54f21155f.png" alt="装饰" class="decoration-left" />
            <img src="/uploads/sites/1012/2022/11/93bf81726314b734d59603e54f21155f.png" alt="装饰" class="decoration-right" />
          </div>
        </div>
      </section>

      <!-- 智慧生态模块 -->
      <section class="smart-ecology">
        <div class="container">
          <div class="section-header">
            <h2>智慧生態</h2>
            <p class="section-subtitle">運用先進技術構建智慧生態監測與管理體系</p>
          </div>
          <div class="ecology-grid">
            <div class="ecology-item pulse-hover" @click="navigateTo('/smart-ecology/monitoring')">
              <div class="item-icon">
                <img src="/uploads/sites/1012/2022/11/073346750cbf5e05e3ea59df9549578a.png" alt="生態監測" />
              </div>
              <h3>生態監測</h3>
              <p>實時監測生態環境變化，提供科學數據支撐</p>
            </div>
            <div class="ecology-item pulse-hover" @click="navigateTo('/smart-ecology/warning')">
              <div class="item-icon">
                <img src="/uploads/sites/1012/2022/11/d1fba75503642fc884e617bc2a146ff9.png" alt="災害預警" />
              </div>
              <h3>災害預警</h3>
              <p>智能預警系統，及時發現潛在風險</p>
            </div>
            <div class="ecology-item pulse-hover" @click="navigateTo('/smart-ecology/fire-prevention')">
              <div class="item-icon">
                <img src="/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png" alt="森林防火" />
              </div>
              <h3>森林防火</h3>
              <p>全方位森林火災防控解決方案</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 人工智能模块 -->
      <section class="ai-section">
        <div class="container">
          <div class="section-header">
            <h2>人工智能</h2>
            <p class="section-subtitle">領先的AI技術與解決方案</p>
          </div>
          <div class="ai-content">
            <div class="ai-category">
              <h3>垂類大模型</h3>
              <div class="ai-grid">
                <div class="ai-item" @click="navigateTo('/ai/multimodal')">
                  <img src="/uploads/sites/1012/2024/07/83841dc18fcc7f97315235a2ac571d46.png" alt="多模態融合" />
                  <span>多模態融合</span>
                  <p>整合文本、圖像、語音等多種數據模態</p>
                </div>
                <div class="ai-item" @click="navigateTo('/ai/training')">
                  <img src="/uploads/sites/1012/2024/07/34cf3b25d4852b8410937873b5be53f8.png" alt="大模型訓練" />
                  <span>大模型訓練</span>
                  <p>專業的大規模模型訓練服務</p>
                </div>
                <div class="ai-item" @click="navigateTo('/ai/nlp')">
                  <img src="/uploads/sites/1012/2024/07/b6ac1fea464d47ee66ddcc796b92268e.png" alt="自然語言處理" />
                  <span>自然語言處理</span>
                  <p>先進的NLP技術與應用</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


      <!-- 新闻中心 -->
      <section class="news-center">
        <div class="container">
          <div class="section-header">
            <p class="section-subtitle">NEWS CENTER</p>
            <h2>新聞中心</h2>
          </div>
          <div class="news-list">
            <div class="news-item">
              <div class="news-image">
                <img src="/uploads/sites/1012/2024/08/c4a51cfaac61c49721e8aac314b36b58.png" alt="新闻图片" />
              </div>
              <div class="news-content">
                <h3>行業研究 I 我國生成式人工智能拐點探究</h3>
              </div>
            </div>
          </div>
          <button class="view-more-btn">查看更多 >></button>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import HeroSlider from '@/components/common/HeroSlider.vue'

// 响应式数据
const router = useRouter()

const slides = ref([
  {
    background: '/uploads/sites/1012/2022/11/a28adbebd944acf1203439567578dc64.jpg',
    logo: '/uploads/sites/1012/2024/12/49894a4535b5c9c8e99b1e377f624758.png',
    title: '數字起源',
    description: '以數字科技為基礎、以科技創新賦能美好生活',
    link: '/about'
  },
  {
    background: '/uploads/sites/1012/2022/11/文旅.jpg',
    logo: '/uploads/sites/1012/2022/12/8c2ec5739cfdd126fc68f6470b455444.png',
    title: '智慧文旅',
    description: '首創基於聯盟鏈應用的全場景目的地雲服務解決方案，以"智旅鏈 + 慧旅雲"雙擎驅動旅遊目的地數字化進程。',
    link: '/smart-culturetourism'
  },
  {
    background: '/uploads/sites/1012/2022/11/002c39e262ac1571f6e62d7f1c7bccdf.jpg',
    logo: '/uploads/sites/1012/2024/12/0ebb375deb8d0be40a02738f15abe603.png',
    title: '智慧城市',
    description: '創新構建基於 AI 城市大腦的"一屏觀城鄉""一網管全域""一語惠全城"基礎模塊，特色打造四大維度智慧應用。',
    link: '/smart-city'
  },
  {
    background: '/uploads/sites/1012/2022/11/b3179e560087fe203f3917d9fcfc0ea7.jpg',
    logo: '/uploads/sites/1012/2022/12/d19ea5a2314a580bbee680fbeade2000.png',
    title: '智慧生態',
    description: '面向自然保護地典型場景，採用"七橫兩縱"邏輯架構，六大模塊全流程賦能智慧生態數字化。',
    link: '/smart-ecology'
  },
  {
    background: '/uploads/sites/1012/2024/07/d508f04c957ff3b6dc0beb7539f56d2b.jpg',
    logo: '/uploads/sites/1012/2024/09/f63e538e9aa6f6e80699de56ee275ac7.png',
    title: 'AIGC應用',
    description: '創新構建基於AIGC的諮詢服務、行程規劃、導遊導覽、智能營銷、品牌推廣、輿情管理等智慧化應用體系。',
    link: '/aigc-applications'
  },
  {
    background: '/uploads/sites/1012/2024/07/268767b6fb7fadf7e59c6fe749925588.jpg',
    logo: '/uploads/sites/1012/2024/09/e11c105c07df6a8d776b21d6756060ef.png',
    title: '數金雲/數科雲',
    description: '公司持續加大研發投入，在數金雲、數科雲、區塊鏈應用等通用方面的產品和服務日益成熟，為眾多行業用戶賦能增效。',
    link: '/digital-cloud'
  }
])

// 方法
const handleLearnMore = (slide) => {
  router.push(slide.link)
}

const handleSlideChange = (slideIndex) => {
  // 可以在这里处理幻灯片切换事件，比如统计或其他逻辑
  console.log('当前幻灯片索引:', slideIndex)
}

const navigateTo = (path) => {
  router.push(path)
}

const initCounters = () => {
  // 数字计数动画
  const animateCounter = (counter) => {
    const target = parseInt(counter.getAttribute('data-target'))
    const increment = target / 100
    let current = 0

    const updateCounter = () => {
      if (current < target) {
        current += increment
        counter.textContent = Math.ceil(current)
        requestAnimationFrame(updateCounter)
      } else {
        counter.textContent = target
      }
    }

    updateCounter()
  }

  // 使用 Intersection Observer 来触发动画
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const counter = entry.target.querySelector('.counter')
        if (counter && !counter.classList.contains('animated')) {
          counter.classList.add('animated')
          animateCounter(counter)
        }
      }
    })
  })

  document.querySelectorAll('.stat-card').forEach(card => {
    observer.observe(card)
  })
}

// 业务布局相关方法
const initBusinessLayout = () => {
  // 初始化业务卡片动画
  const observeBusinessCards = () => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in')
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    })

    document.querySelectorAll('.business-card').forEach(card => {
      observer.observe(card)
    })
  }

  // 业务卡片项目点击处理
  const handleBusinessItemClick = (itemType, itemName) => {
    console.log(`点击了业务项目: ${itemType} - ${itemName}`)
    // 这里可以添加具体的跳转逻辑或弹窗显示详情
    // 例如：router.push(`/business/${itemType}/${itemName}`)
  }

  // 为业务卡片项目添加点击事件
  document.querySelectorAll('.card-item').forEach(item => {
    item.addEventListener('click', () => {
      const itemText = item.querySelector('.item-text')?.textContent
      const sectionTitle = item.closest('.business-section')?.querySelector('.section-title')?.textContent
      if (itemText && sectionTitle) {
        handleBusinessItemClick(sectionTitle, itemText)
      }
    })
  })

  observeBusinessCards()
}

// 业务布局数据
const businessSections = ref([
  {
    id: 'industry-cloud',
    title: '行業雲',
    items: [
      {
        name: '智慧文旅',
        subItems: [
          { name: '數字景區', icon: '/uploads/sites/1012/2022/11/22093492e9cda93d64e9df82cca6706e.png' },
          { name: '智能營銷', icon: '/uploads/sites/1012/2022/11/cca01f8608a48e4299e118c4f86a71fb.png' },
          { name: '智慧生態', icon: '/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png' }
        ]
      }
    ]
  },
  {
    id: 'smart-ecology',
    title: '智慧生態',
    items: [
      {
        name: '智慧生態',
        subItems: [
          { name: '生態監測', icon: '/uploads/sites/1012/2022/11/073346750cbf5e05e3ea59df9549578a.png' },
          { name: '災害預警', icon: '/uploads/sites/1012/2022/11/d1fba75503642fc884e617bc2a146ff9.png' },
          { name: '森林防火', icon: '/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png' }
        ]
      }
    ]
  },
  {
    id: 'artificial-intelligence',
    title: '人工智能',
    items: [
      {
        name: '垂類大模型',
        subItems: [
          { name: '智能分析', icon: '/uploads/sites/1012/2024/07/83841dc18fcc7f97315235a2ac571d46.png' },
          { name: '智能決策', icon: '/uploads/sites/1012/2022/11/cca01f8608a48e4299e118c4f86a71fb.png' },
          { name: '智能應用', icon: '/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png' }
        ]
      }
    ]
  }
])

// 生命周期钩子
onMounted(() => {
  initCounters()
  initBusinessLayout()
})
</script>

<style scoped>
/* 基础样式将在单独的样式文件中定义 */
@import './styles.css';
</style>
